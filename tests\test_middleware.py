#!/usr/bin/env python3
"""
Test script for Ryu Middleware API

This script tests the basic functionality of the middleware API
to ensure it works correctly and doesn't affect existing functionality.
"""

import requests
import json
import time
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
LOG = logging.getLogger(__name__)

# API base URL
BASE_URL = "http://localhost:8080"

def test_health_check():
    """Test health check endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/v2.0/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            LOG.info("✓ Health check passed")
            LOG.info(f"  Status: {data}")
            return True
        else:
            LOG.error(f"✗ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        LOG.error(f"✗ Health check error: {e}")
        return False

def test_topology_view():
    """Test topology view endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/v2.0/topology/view", timeout=5)
        if response.status_code == 200:
            data = response.json()
            LOG.info("✓ Topology view passed")
            LOG.info(f"  Switches: {len(data.get('data', {}).get('switches', []))}")
            return True
        else:
            LOG.error(f"✗ Topology view failed: {response.status_code}")
            return False
    except Exception as e:
        LOG.error(f"✗ Topology view error: {e}")
        return False

def test_topology_status():
    """Test topology status endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/v2.0/topology/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            LOG.info("✓ Topology status passed")
            LOG.info(f"  Status: {data.get('data', {}).get('status', 'unknown')}")
            return True
        else:
            LOG.error(f"✗ Topology status failed: {response.status_code}")
            return False
    except Exception as e:
        LOG.error(f"✗ Topology status error: {e}")
        return False

def test_host_list():
    """Test host list endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/v2.0/host/list", timeout=5)
        # This might return an error if no topology is active, which is OK
        LOG.info("✓ Host list endpoint accessible")
        LOG.info(f"  Response: {response.status_code}")
        return True
    except Exception as e:
        LOG.error(f"✗ Host list error: {e}")
        return False

def test_stats_endpoints():
    """Test statistics endpoints"""
    endpoints = [
        "/v2.0/stats/flow",
        "/v2.0/stats/port", 
        "/v2.0/stats/packet",
        "/v2.0/stats/topology"
    ]
    
    success_count = 0
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            if response.status_code == 200:
                LOG.info(f"✓ {endpoint} passed")
                success_count += 1
            else:
                LOG.warning(f"⚠ {endpoint} returned {response.status_code}")
        except Exception as e:
            LOG.warning(f"⚠ {endpoint} error: {e}")
    
    LOG.info(f"Stats endpoints: {success_count}/{len(endpoints)} accessible")
    return success_count > 0

def test_ml_endpoints():
    """Test ML integration endpoints"""
    try:
        # Test models list
        response = requests.get(f"{BASE_URL}/v2.0/ml/models", timeout=5)
        if response.status_code == 200:
            LOG.info("✓ ML models endpoint passed")
        elif response.status_code == 503:
            LOG.info("✓ ML integration disabled (expected)")
        else:
            LOG.warning(f"⚠ ML models returned {response.status_code}")
        
        return True
    except Exception as e:
        LOG.warning(f"⚠ ML endpoints error: {e}")
        return False

def test_existing_api_compatibility():
    """Test that existing v1.0 APIs still work"""
    try:
        # Test existing topology API
        response = requests.get(f"{BASE_URL}/v1.0/topology/switches", timeout=5)
        if response.status_code == 200:
            LOG.info("✓ Existing v1.0 topology API still works")
            return True
        else:
            LOG.warning(f"⚠ v1.0 API returned {response.status_code}")
            return False
    except Exception as e:
        LOG.warning(f"⚠ v1.0 API error: {e}")
        return False

def test_topology_creation():
    """Test topology creation with a simple topology"""
    try:
        # Simple test topology
        topology = {
            "name": "test_topology",
            "switches": [
                {"name": "s1"}
            ],
            "hosts": [
                {"name": "h1", "ip": "********"},
                {"name": "h2", "ip": "********"}
            ],
            "links": [
                {"src": "h1", "dst": "s1"},
                {"src": "h2", "dst": "s1"}
            ]
        }
        
        response = requests.post(
            f"{BASE_URL}/v2.0/topology/create",
            json=topology,
            timeout=10
        )
        
        if response.status_code in [200, 201]:
            LOG.info("✓ Topology creation endpoint works")
            
            # Try to delete the topology
            time.sleep(1)
            delete_response = requests.delete(f"{BASE_URL}/v2.0/topology/delete", timeout=10)
            if delete_response.status_code == 200:
                LOG.info("✓ Topology deletion endpoint works")
            
            return True
        else:
            data = response.json() if response.headers.get('content-type') == 'application/json' else {}
            LOG.info(f"✓ Topology creation endpoint accessible (status: {response.status_code})")
            LOG.info(f"  Response: {data.get('message', 'No message')}")
            return True
            
    except Exception as e:
        LOG.warning(f"⚠ Topology creation test error: {e}")
        return False

def run_tests():
    """Run all tests"""
    LOG.info("Starting Ryu Middleware API Tests")
    LOG.info("=" * 50)
    
    tests = [
        ("Health Check", test_health_check),
        ("Topology View", test_topology_view),
        ("Topology Status", test_topology_status),
        ("Host List", test_host_list),
        ("Statistics Endpoints", test_stats_endpoints),
        ("ML Endpoints", test_ml_endpoints),
        ("v1.0 API Compatibility", test_existing_api_compatibility),
        ("Topology Creation", test_topology_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        LOG.info(f"\nRunning: {test_name}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            LOG.error(f"✗ {test_name} failed with exception: {e}")
    
    LOG.info("\n" + "=" * 50)
    LOG.info(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        LOG.info("🎉 All tests passed! Middleware API is working correctly.")
        return True
    elif passed >= total * 0.7:  # 70% pass rate
        LOG.info("✅ Most tests passed. Middleware API is mostly functional.")
        return True
    else:
        LOG.error("❌ Many tests failed. Please check the middleware setup.")
        return False

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
